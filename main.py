#!/usr/bin/env python3
"""
ML Log Prediction with GUI file selection dialog for LAS files.
"""

from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import get_input_files, select_output_directory, configure_log_selection, configure_well_separation, get_prediction_mode, configure_hyperparameters, console_select
from ml_core import impute_logs, impute_logs_deep, MODEL_REGISTRY
from reporting import (generate_qc_report, create_summary_plots, generate_final_report,
                      create_multi_model_comparison_plots, create_separate_comparison_plots,
                      create_crossplot_analysis, create_model_ranking_visualization)

def main():
    """Run the ML log prediction workflow with GUI file selection."""
    print("=" * 60)
    print(" ML LOG PREDICTION")
    print("=" * 60)
    
    # Step 1: Get input files using file dialog
    print("\n📁 Step 1: Select LAS files")
    inp = get_input_files()
    
    if not inp:
        print("❌ File selection cancelled. Exiting.")
        return
    
    # Step 2: Load LAS files
    print("\n📊 Step 2: Loading LAS files...")
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    
    if df.empty:
        print("❌ No data loaded. Exiting.")
        return
    
    print(f"✅ Successfully loaded:")
    print(f"   • {len(df)} data points")
    print(f"   • {len(wells)} wells: {', '.join(wells)}")
    print(f"   • {len(logs)} log curves: {', '.join(logs)}")
    
    # Step 3: Configure log selection
    print("\n🎯 Step 3: Configure feature and target logs")
    feats, tgt = configure_log_selection(logs)
    print(f"✅ Feature logs: {', '.join(feats)}")
    print(f"✅ Target log: {tgt}")
    
    # Step 4: Configure well separation
    print("\n🏗️ Step 4: Configure training/prediction strategy")
    cfg = configure_well_separation(wells)
    print(f"✅ Mode: {cfg['mode']}")
    
    # Step 5: Configure prediction mode
    print("\n⚙️ Step 5: Configure prediction mode")
    mode = get_prediction_mode()
    print(f"✅ Prediction mode: {mode}")
    
    # Step 6: Configure hyperparameters
    print("\n🔧 Step 6: Configure model hyperparameters")
    hparams = configure_hyperparameters()
    print("✅ Using default hyperparameters for all models")
    
    # Step 7: Data cleaning and QC
    print("\n🧹 Step 7: Data cleaning and quality control")
    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)

    # Main processing loop - allows running multiple models
    while True:
        # Step 8: Machine learning prediction
        print("\n🤖 Step 8: Running machine learning models...")

        # Multi-model selection interface
        available_models = list(MODEL_REGISTRY.keys())
        print("\nModel selection options:")
        print("• Select multiple models by entering comma-separated numbers (e.g., 1,2,3)")
        print("• Type 'all' to select all available models")
        print("• Press Enter for default single model selection")

        selected_model_keys = console_select(
            available_models,
            "Select model(s) to run",
            default=['xgboost'],
            multiple=True
        )

        # Ensure we have a list even for single selection
        if not isinstance(selected_model_keys, list):
            selected_model_keys = [selected_model_keys]

        # Flatten any nested lists and ensure all items are strings
        flattened_models = []
        for item in selected_model_keys:
            if isinstance(item, list):
                flattened_models.extend(item)
            else:
                flattened_models.append(item)
        selected_model_keys = flattened_models

        print(f"✅ Selected models: {', '.join(selected_model_keys)}")

        # Batch model execution
        all_results = {}
        combined_evaluations = []
        successful_models = []
        failed_models = []

        print(f"\n🔄 Running {len(selected_model_keys)} model(s)...")

        for i, model_key in enumerate(selected_model_keys, 1):
            print(f"\n--- Running Model {i}/{len(selected_model_keys)}: {model_key} ---")
            selected_model_config = MODEL_REGISTRY[model_key]

            try:
                # Check the model type to decide which function to call
                if selected_model_config.get('type') == 'deep':
                    # Run the deep learning workflow
                    hparams_selected = hparams[model_key]
                    res_df, mres = impute_logs_deep(clean_df, feats, tgt, selected_model_config, hparams_selected)
                else:
                    # Run the existing shallow model workflow
                    models_to_run = {selected_model_config['name']: selected_model_config['model_class'](**hparams[model_key])}
                    res_df, mres = impute_logs(clean_df, feats, tgt, models_to_run, cfg, mode)

                if mres and res_df is not None:
                    # Store results for this model
                    all_results[model_key] = {
                        'res_df': res_df,
                        'mres': mres,
                        'model_config': selected_model_config
                    }

                    # Add evaluations to combined list with model identifier
                    for eval_result in mres['evaluations']:
                        eval_with_key = eval_result.copy()
                        eval_with_key['model_key'] = model_key
                        combined_evaluations.append(eval_with_key)

                    successful_models.append(model_key)
                    print(f"✅ {model_key} completed successfully")
                else:
                    failed_models.append(model_key)
                    print(f"❌ {model_key} failed to produce results")

            except Exception as e:
                failed_models.append(model_key)
                print(f"❌ {model_key} failed with error: {str(e)}")

        # Results summary
        print(f"\n📊 Batch execution summary:")
        print(f"   • Successful models: {len(successful_models)}")
        print(f"   • Failed models: {len(failed_models)}")

        if successful_models:
            print(f"   • ✅ Successful: {', '.join(successful_models)}")
        if failed_models:
            print(f"   • ❌ Failed: {', '.join(failed_models)}")

        if not successful_models:
            print("❌ All models failed. Continuing to next step...")
            res_df, mres = None, None
        else:
            print("✅ Multi-model execution completed")

        # Step 9: Configure output options
        print("\n💾 Step 9: Configure output options")

        if not successful_models:
            print("⚠️ No successful models to process. Skipping to next step...")
        else:
            # Display model performance comparison
            if len(successful_models) > 1:
                print("\n📈 Model Performance Comparison:")
                print("-" * 60)
                print(f"{'Model':<15} {'MAE':<10} {'R²':<10} {'Composite':<12}")
                print("-" * 60)

                # Sort evaluations by composite score for ranking
                sorted_evals = sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))

                for eval_result in sorted_evals:
                    model_name = eval_result.get('model_name', eval_result.get('model_key', 'Unknown'))
                    mae = eval_result.get('mae', 0)
                    r2 = eval_result.get('r2', 0)
                    composite = eval_result.get('composite_score', 0)
                    print(f"{model_name:<15} {mae:<10.3f} {r2:<10.3f} {composite:<12.3f}")

                print("-" * 60)
                best_model = sorted_evals[0]
                print(f"🏆 Best performing model: {best_model.get('model_name', best_model.get('model_key'))}")

            # Model selection for output
            if len(successful_models) == 1:
                selected_output_model = successful_models[0]
                print(f"\n📊 Using results from: {selected_output_model}")
            else:
                print(f"\nSelect which model's results to use for output:")
                selected_output_model = console_select(
                    successful_models,
                    "Choose model for output",
                    default=successful_models[0]
                )

            # Get the selected model's results
            selected_results = all_results[selected_output_model]
            res_df = selected_results['res_df']
            mres = selected_results['mres']

            print(f"✅ Selected {selected_output_model} for output processing")

            # Enhanced output options
            print("\nSelect visualization and output options:")
            print("1. Save results to files (with standard plots)")
            print("2. Enhanced visualization analysis (no file output)")
            print("3. Comprehensive model comparison and ranking")
            print("4. Quality control analysis (cross-plots and statistics)")

            output_handled = False
            while not output_handled:
                choice = input("Enter choice (1, 2, 3, or 4): ").strip()
                if choice == "1":
                    # Get output directory
                    print("\n📁 Select output directory...")
                    out = select_output_directory()
                    if not out:
                        print("❌ Output directory selection cancelled. Skipping file output.")
                        continue

                    # Generate and save results
                    print("\n📈 Generating and saving results...")
                    create_summary_plots(res_df, mres, cfg, model_name=selected_output_model, show_error_bands=True)
                    write_results_to_las(res_df, tgt, las_objs, out)
                    generate_final_report(mres)

                    print("\n🎉 Results processing completed!")
                    print(f"📁 Results saved to: {out}")
                    output_handled = True

                elif choice == "2":
                    # Enhanced visualization analysis
                    print("\n📈 Generating enhanced visualization analysis...")

                    # Separate comparison plots for better clarity
                    print("📊 Creating separate Original vs Imputed and Original vs Predicted plots...")
                    create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)

                    # Cross-plot analysis for quality control
                    print("📊 Creating cross-plot analysis for quality control...")
                    create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')

                    print("\n🎉 Enhanced visualization analysis completed!")
                    print("📊 Separate plots and cross-plot analysis generated for detailed review")
                    output_handled = True

                elif choice == "3":
                    # Comprehensive model comparison and ranking
                    if len(successful_models) == 1:
                        print("\n⚠️ Only one model available. Showing detailed analysis for single model...")
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')
                    else:
                        print("\n📈 Generating comprehensive model comparison and ranking...")

                        # Model ranking visualization
                        print("📊 Creating model ranking visualization...")
                        create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                        # Individual model plots
                        print("📊 Creating individual model plots...")
                        for model_key in successful_models:
                            model_results = all_results[model_key]
                            print(f"   • Generating plots for {model_key}...")
                            create_separate_comparison_plots(model_results['res_df'], model_results['mres'], cfg,
                                                           model_name=model_key)

                        # Multi-model comparison plot
                        print("📊 Creating multi-model comparison plot...")
                        create_multi_model_comparison_plots(all_results, cfg, tgt)

                        # Combined performance report
                        print("📊 Generating combined performance report...")
                        combined_mres = {
                            'target': tgt,
                            'evaluations': combined_evaluations,
                            'best_model_name': sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))[0].get('model_name', 'Unknown')
                        }
                        generate_final_report(combined_mres)

                    print("\n🎉 Comprehensive model comparison and ranking completed!")
                    print("📊 All comparison plots, rankings, and analysis generated for review")
                    output_handled = True

                elif choice == "4":
                    # Quality control analysis
                    print("\n📈 Generating quality control analysis...")

                    if len(successful_models) == 1:
                        # Single model QC analysis
                        print("📊 Creating detailed quality control analysis...")
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='depth')
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                    else:
                        # Multi-model QC analysis
                        print("📊 Creating cross-plot analysis for all models...")
                        for model_key in successful_models:
                            model_results = all_results[model_key]
                            print(f"   • QC analysis for {model_key}...")
                            create_crossplot_analysis(model_results['res_df'], model_results['mres'], cfg,
                                                     model_name=model_key, color_by='well')

                        # Model ranking for QC
                        print("📊 Creating model ranking for quality assessment...")
                        create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                    print("\n🎉 Quality control analysis completed!")
                    print("📊 Cross-plots, statistics, and quality metrics generated for review")
                    output_handled = True

                else:
                    print("Invalid choice. Please enter 1, 2, 3, or 4.")

        # Step 10: Continue or Exit menu
        print("\n🔄 Step 10: Next action")
        print("What would you like to do next?")
        print("1. Exit the program")
        print("2. Continue processing (run another model)")
        print("3. Go back to plot options (Step 9)")

        step_10_handled = False
        while not step_10_handled:
            choice = input("Enter choice (1, 2, or 3): ").strip()
            if choice == "1":
                print("\n👋 Exiting program. Thank you for using ML Log Prediction!")
                return
            elif choice == "2":
                print("\n🔄 Continuing to model selection...")
                step_10_handled = True
                break  # Break out of Step 10 menu to continue the main loop
            elif choice == "3":
                print("\n🔙 Going back to plot options...")
                # Go back to Step 9 plot options
                if not successful_models:
                    print("⚠️ No successful models to process. Cannot return to plot options.")
                    continue

                # Re-run the Step 9 output options
                print("\n💾 Step 9: Configure output options (Revisited)")
                print("\nSelect visualization and output options:")
                print("1. Save results to files (with standard plots)")
                print("2. Enhanced visualization analysis (no file output)")
                print("3. Comprehensive model comparison and ranking")
                print("4. Quality control analysis (cross-plots and statistics)")

                output_handled = False
                while not output_handled:
                    plot_choice = input("Enter choice (1, 2, 3, or 4): ").strip()
                    if plot_choice == "1":
                        # Get output directory
                        print("\n📁 Select output directory...")
                        out = select_output_directory()
                        if not out:
                            print("❌ Output directory selection cancelled. Skipping file output.")
                            continue

                        # Generate and save results
                        print("\n📈 Generating and saving results...")
                        create_summary_plots(res_df, mres, cfg, model_name=selected_output_model, show_error_bands=True)
                        write_results_to_las(res_df, tgt, las_objs, out)
                        generate_final_report(mres)

                        print("\n🎉 Results processing completed!")
                        print(f"📁 Results saved to: {out}")
                        output_handled = True

                    elif plot_choice == "2":
                        # Enhanced visualization analysis
                        print("\n📈 Generating enhanced visualization analysis...")

                        # Separate comparison plots for better clarity
                        print("📊 Creating separate Original vs Imputed and Original vs Predicted plots...")
                        create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)

                        # Cross-plot analysis for quality control
                        print("📊 Creating cross-plot analysis for quality control...")
                        create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')

                        print("\n🎉 Enhanced visualization analysis completed!")
                        print("📊 Separate plots and cross-plot analysis generated for detailed review")
                        output_handled = True

                    elif plot_choice == "3":
                        # Comprehensive model comparison and ranking
                        if len(successful_models) == 1:
                            print("\n⚠️ Only one model available. Showing detailed analysis for single model...")
                            create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                            create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='well')
                        else:
                            print("\n📈 Generating comprehensive model comparison and ranking...")

                            # Model ranking visualization
                            print("📊 Creating model ranking visualization...")
                            create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                            # Individual model plots
                            print("📊 Creating individual model plots...")
                            for model_key in successful_models:
                                model_results = all_results[model_key]
                                print(f"   • Generating plots for {model_key}...")
                                create_separate_comparison_plots(model_results['res_df'], model_results['mres'], cfg,
                                                               model_name=model_key)

                            # Multi-model comparison plot
                            print("📊 Creating multi-model comparison plot...")
                            create_multi_model_comparison_plots(all_results, cfg, tgt)

                            # Combined performance report
                            print("📊 Generating combined performance report...")
                            combined_mres = {
                                'target': tgt,
                                'evaluations': combined_evaluations,
                                'best_model_name': sorted(combined_evaluations, key=lambda x: x.get('composite_score', float('inf')))[0].get('model_name', 'Unknown')
                            }
                            generate_final_report(combined_mres)

                        print("\n🎉 Comprehensive model comparison and ranking completed!")
                        print("📊 All comparison plots, rankings, and analysis generated for review")
                        output_handled = True

                    elif plot_choice == "4":
                        # Quality control analysis
                        print("\n📈 Generating quality control analysis...")

                        if len(successful_models) == 1:
                            # Single model QC analysis
                            print("📊 Creating detailed quality control analysis...")
                            create_crossplot_analysis(res_df, mres, cfg, model_name=selected_output_model, color_by='depth')
                            create_separate_comparison_plots(res_df, mres, cfg, model_name=selected_output_model)
                        else:
                            # Multi-model QC analysis
                            print("📊 Creating cross-plot analysis for all models...")
                            for model_key in successful_models:
                                model_results = all_results[model_key]
                                print(f"   • QC analysis for {model_key}...")
                                create_crossplot_analysis(model_results['res_df'], model_results['mres'], cfg,
                                                         model_name=model_key, color_by='well')

                            # Model ranking for QC
                            print("📊 Creating model ranking for quality assessment...")
                            create_model_ranking_visualization(all_results, tgt, combined_evaluations)

                        print("\n🎉 Quality control analysis completed!")
                        print("📊 Cross-plots, statistics, and quality metrics generated for review")
                        output_handled = True

                    else:
                        print("Invalid choice. Please enter 1, 2, 3, or 4.")

                # After completing Step 9 again, continue to Step 10
                continue
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")

        # Continue to next iteration of main loop (back to Step 8)

if __name__ == "__main__":
    main()
