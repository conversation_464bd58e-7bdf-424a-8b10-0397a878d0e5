# Enhanced Preprocessing for ML Log Prediction

## Overview

This guide describes the enhanced preprocessing pipeline that integrates advanced techniques from `cp_preconditioning/` with deep learning best practices to improve autoencoder and U-Net model performance.

## Key Improvements

### 🎯 **Statistical Outlier Removal (Winsorization)**
- **Current**: Hard-coded thresholds (e.g., GR: 0-300)
- **Enhanced**: Statistical percentile-based outlier detection (1st/99th percentiles)
- **Benefit**: More robust outlier detection, prevents gradient explosion in neural networks

### 🔗 **Intelligent Sequence Creation**
- **Current**: Simple sequential slicing, may include NaN gaps
- **Enhanced**: Valid interval detection, ensures continuous sequences
- **Benefit**: Better training data quality for sequence-based models

### ❓ **Realistic Missing Data Patterns**
- **Current**: Random missing value introduction
- **Enhanced**: Mix of random (30%) and chunked (70%) missing patterns
- **Benefit**: More realistic training scenarios reflecting real well log conditions

### 📊 **Enhanced Normalization**
- **Current**: Basic StandardScaler with NaN handling
- **Enhanced**: Multi-well consistent scaling, robust scaler options
- **Benefit**: Better cross-well consistency and gradient-friendly distributions

## Usage

### Quick Start

```python
from enhanced_preprocessing import enhanced_preprocessing_pipeline

# Use the complete enhanced pipeline
sequences, sequences_missing, scalers, report = enhanced_preprocessing_pipeline(
    df=your_dataframe,
    feature_cols=['GR', 'NPHI', 'RHOB'],
    target_col='RT',
    sequence_len=64,
    sequence_stride=32,
    missing_rate=0.2,
    normalization_method='standard',
    winsorize_percentiles=(0.01, 0.99)
)
```

### Integration with Existing Workflow

The enhanced preprocessing can be integrated into the existing workflow in two ways:

#### Option 1: Drop-in Replacement Functions
```python
# In data_handler.py functions, add use_enhanced=True
df_scaled, scalers = normalize_data(df, columns, use_enhanced=True)
sequences = create_sequences(df, 'WELL', features, use_enhanced=True)
sequences_missing = introduce_missingness(sequences, use_enhanced=True)
```

#### Option 2: Enhanced Deep Learning Pipeline
```python
# In ml_core.py, use enhanced preprocessing for deep learning models
res_df, mres = impute_logs_deep(
    df, feature_cols, target_col, model_config, hparams,
    use_enhanced_preprocessing=True  # New parameter
)
```

## Configuration Options

### EnhancedLogPreprocessor Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `winsorize_percentiles` | `(0.01, 0.99)` | Percentiles for outlier clipping |
| `normalization_method` | `'standard'` | `'standard'` or `'robust'` |
| `sequence_len` | `64` | Length of sequences for deep learning |
| `sequence_stride` | `32` | Stride between sequence starts |
| `missing_rate` | `0.2` | Rate of missing values to introduce |
| `random_seed` | `42` | Random seed for reproducibility |

### Recommended Settings by Use Case

#### For Autoencoder Models
```python
# Autoencoder benefits from longer sequences and moderate overlap
sequence_len=64
sequence_stride=32  # 50% overlap
missing_rate=0.15   # Conservative missing rate
normalization_method='standard'
```

#### For U-Net Models
```python
# U-Net can handle shorter sequences with less overlap
sequence_len=32
sequence_stride=24  # 25% overlap
missing_rate=0.2    # Higher missing rate for robustness
normalization_method='robust'  # More robust to outliers
```

#### For Noisy/Problematic Data
```python
# More aggressive preprocessing for challenging datasets
winsorize_percentiles=(0.02, 0.98)  # More aggressive outlier removal
normalization_method='robust'       # Robust to outliers
missing_rate=0.25                   # Higher missing rate for robustness
```

## Preprocessing Pipeline Steps

### Step 1: Statistical Outlier Removal
```
Input: Raw well log data
Process: Calculate 1st/99th percentiles, set outliers to NaN
Output: Data with statistical outliers removed
Benefits: Prevents gradient explosion, more robust than hard thresholds
```

### Step 2: Enhanced Normalization
```
Input: Outlier-cleaned data
Process: Fit scaler on valid data, transform all data preserving NaN
Output: Normalized data with mean≈0, std≈1
Benefits: Multi-well consistency, gradient-friendly distributions
```

### Step 3: Intelligent Sequence Creation
```
Input: Normalized data
Process: Detect valid intervals, create sequences within intervals
Output: Continuous sequences without NaN gaps
Benefits: Better training data quality for sequence models
```

### Step 4: Realistic Missing Value Introduction
```
Input: Clean sequences
Process: Introduce 30% random + 70% chunked missing patterns
Output: Training sequences with realistic missing patterns
Benefits: Better generalization to real-world missing data
```

### Step 5: Quality Validation
```
Input: Clean and missing sequences
Process: Validate data quality, check gradient-friendliness
Output: Quality metrics and validation report
Benefits: Ensures preprocessing quality for deep learning
```

## Expected Performance Improvements

### Data Quality Metrics
- **Outlier Reduction**: 1-3% of extreme values removed
- **Sequence Continuity**: 100% of sequences have complete data
- **Missing Pattern Realism**: 70% chunked patterns vs 100% random
- **Normalization Quality**: Mean ≈ 0, Std ≈ 1, gradient-friendly

### Model Training Benefits
- **Reduced NaN Loss**: Eliminates NaN values in training sequences
- **Better Convergence**: Gradient-friendly data distributions
- **Improved Generalization**: Realistic missing data patterns
- **Stable Training**: Consistent normalization across wells

## Troubleshooting

### Common Issues

#### "No valid sequences could be created"
- **Cause**: Too much missing data or sequence_len too large
- **Solution**: Reduce sequence_len or increase sequence_stride

#### "Enhanced preprocessing not available"
- **Cause**: Import error for enhanced_preprocessing module
- **Solution**: Ensure enhanced_preprocessing.py is in the same directory

#### Warning: "X has feature names, but StandardScaler was fitted without feature names"
- **Cause**: sklearn version compatibility issue
- **Solution**: This is a warning only, preprocessing still works correctly

### Performance Tuning

#### For Large Datasets
```python
# Reduce memory usage
sequence_stride = sequence_len  # No overlap
missing_rate = 0.1             # Lower missing rate
```

#### For Small Datasets
```python
# Maximize data usage
sequence_stride = sequence_len // 4  # 75% overlap
missing_rate = 0.3                  # Higher missing rate for robustness
```

## Validation and Testing

Run the test scripts to validate the enhanced preprocessing:

```bash
# Simple validation test
python test_enhanced_simple.py

# Comprehensive test with visualizations
python test_enhanced_preprocessing.py
```

## Integration Checklist

- [ ] Copy `enhanced_preprocessing.py` to your project directory
- [ ] Update `data_handler.py` imports (already done)
- [ ] Update `ml_core.py` for deep learning models (already done)
- [ ] Test with your data using `test_enhanced_simple.py`
- [ ] Configure parameters for your specific use case
- [ ] Monitor preprocessing reports for data quality
- [ ] Compare model performance before/after enhanced preprocessing

## Next Steps

1. **Test with Real Data**: Run enhanced preprocessing on your actual well log data
2. **Performance Comparison**: Compare autoencoder/U-Net performance with and without enhanced preprocessing
3. **Parameter Tuning**: Adjust parameters based on your data characteristics
4. **Integration**: Integrate enhanced preprocessing into your production workflow

The enhanced preprocessing pipeline is designed to be backward-compatible while providing significant improvements for deep learning model performance on well log data.
