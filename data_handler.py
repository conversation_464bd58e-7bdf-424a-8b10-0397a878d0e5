import os, glob
import copy
import lasio
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

# Import enhanced preprocessing (optional)
try:
    from enhanced_preprocessing import (
        enhanced_preprocessing_pipeline,
        enhanced_normalize_data,
        enhanced_create_sequences,
        enhanced_introduce_missingness
    )
    ENHANCED_PREPROCESSING_AVAILABLE = True
except ImportError:
    ENHANCED_PREPROCESSING_AVAILABLE = False
    print("Enhanced preprocessing not available. Using standard preprocessing.")

def load_las_files_from_directory(input_source):
    """Load LAS files from a directory or list of file paths into a DataFrame."""
    # Handle both directory path (string) and list of file paths
    if isinstance(input_source, str):
        # Input is a directory path
        las_files = glob.glob(os.path.join(input_source, '*.las'))
        if not las_files:
            print("No LAS files found in directory.")
            return pd.DataFrame(), {}, [], []
    elif isinstance(input_source, list):
        # Input is a list of file paths
        las_files = input_source
        if not las_files:
            print("No LAS files provided.")
            return pd.DataFrame(), {}, [], []
    else:
        print("Invalid input source. Must be directory path or list of file paths.")
        return pd.DataFrame(), {}, [], []

    frames, las_objs, wells = [], {}, []
    for f in las_files:
        try:
            las = lasio.read(f)
            well = las.well.WELL.value or os.path.splitext(os.path.basename(f))[0]

            # Convert LAS to DataFrame - handle different lasio versions
            if hasattr(las, 'to_df') and callable(las.to_df):
                df = las.to_df().reset_index()
            elif hasattr(las, 'df') and callable(las.df):
                df = las.df().reset_index()
            else:
                # Fallback - try to access df as property
                df = las.df.reset_index()

            depth_col = next((c for c in ['DEPT','DEPTH','MD'] if c in df.columns), None)
            if depth_col is None:
                print(f"{f}: no depth column.")
                continue
            df.rename(columns={depth_col:'MD'}, inplace=True)
            df['WELL'] = well
            frames.append(df)
            las_objs[well] = las
            wells.append(well)
            print(f"Loaded {well}")
        except Exception as e:
            print(f"Error {f}: {e}")

    if not frames:
        return pd.DataFrame(), {}, [], []

    comb = pd.concat(frames, ignore_index=True)
    log_names = [c for c in comb.columns if c not in ['MD','WELL']]
    return comb, las_objs, sorted(set(wells)), sorted(log_names)

def clean_log_data(df):
    """Basic cleaning."""
    rules = {'GR':(0,300),'NPHI':(0,1),'RHOB':(1.5,3.0),'DT':(40,200)}
    clean = df.copy()
    for col,(mn,mx) in rules.items():
        if col in clean.columns:
            clean[col] = np.where((clean[col]>=mn)&(clean[col]<=mx),clean[col],np.nan)
    return clean

def write_results_to_las(res_df, target_log, las_objs, out_dir):
    os.makedirs(out_dir, exist_ok=True)
    imp, pred, err = (f"{target_log}_imputed", f"{target_log}_pred", f"{target_log}_error")
    for well, las in las_objs.items():
        wdf = res_df[res_df['WELL']==well]
        if wdf.empty: continue
        new_las = copy.deepcopy(las)
        for mn in [imp,pred,err]:
            if mn in new_las.curves: new_las.delete_curve(mn)
        unit = las.curves[target_log].unit if target_log in las.curves else ""
        for mn,desc in [(imp,f"ML Imputed {target_log}"),(pred,f"ML Pred {target_log}"),(err,f"ML Err {target_log}")]:
            data = wdf.set_index('MD')[mn].reindex(new_las.index).values
            new_las.append_curve(mn, data, unit=unit, descr=desc)
        out = os.path.join(out_dir, f"{well}_imputed.las")
        new_las.write(out, version=2.0)
        print(f"Wrote {out}")

def normalize_data(df, columns, use_enhanced=False, scalers=None):
    """Normalizes the specified columns in the dataframe with improved NaN handling."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced normalization with winsorization...")
        return enhanced_normalize_data(df, columns, scalers=scalers)

    # Standard normalization
    df_scaled = df.copy()

    if scalers is None:
        # Fit and apply new scalers
        new_scalers = {}
        print("Fitting new scalers...")
        for col in columns:
            if col not in df.columns:
                print(f"Warning: Column '{col}' not found in dataframe")
                continue

            valid_data = df[col].dropna()

            if len(valid_data) == 0:
                print(f"Warning: No valid data for column '{col}', skipping normalization")
                new_scalers[col] = None
                continue

            scaler = StandardScaler()
            scaler.fit(valid_data.values.reshape(-1, 1))
            df_scaled[col] = scaler.transform(df[[col]])
            new_scalers[col] = scaler
            print(f"Normalized '{col}': method=standard, valid_data={len(valid_data)}/{len(df)} ({len(valid_data)/len(df):.1%})")
        
        return df_scaled, new_scalers
    else:
        # Apply existing scalers
        print("Applying existing scalers...")
        for col in columns:
            if col in scalers and scalers[col] is not None:
                df_scaled[col] = scalers[col].transform(df[[col]])
                valid_data = df[col].dropna()
                print(f"Normalized '{col}': method=standard, valid_data={len(valid_data)}/{len(df)} ({len(valid_data)/len(df):.1%})")
            else:
                print(f"Warning: Scaler for column '{col}' not found or is None. Column not transformed.")
        
        return df_scaled, scalers

def create_sequences(df, well_col, feature_cols, sequence_len=64, step=1, use_enhanced=False):
    """Creates sequences from well data for deep learning models."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced sequence creation with valid interval detection...")
        return enhanced_create_sequences(df, well_col, feature_cols, sequence_len, step)

    # Standard sequence creation (existing implementation)
    all_sequences = []
    for well in df[well_col].unique():
        well_df = df[df[well_col] == well]
        data = well_df[feature_cols].values
        num_sequences = (len(data) - sequence_len) // step + 1
        for i in range(num_sequences):
            start = i * step
            end = start + sequence_len
            all_sequences.append(data[start:end])
    print(f"Created {len(all_sequences)} sequences of length {sequence_len}.")
    return np.array(all_sequences)

def introduce_missingness(sequences, missing_rate=0.2, random_seed=42, use_enhanced=False,
                         target_col_name=None, feature_names=None):
    """Introduces missing values into the sequences for imputation training with improved strategy."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced missing value introduction with realistic patterns...")
        return enhanced_introduce_missingness(sequences, missing_rate, random_seed)

    # Standard missing value introduction (existing implementation)
    np.random.seed(random_seed)
    sequences_with_missing = sequences.copy()

    # Get original shape
    n_sequences, seq_len, n_features = sequences.shape

    # Strategy: introduce missing values in chunks rather than randomly
    # This is more realistic for well log data where missing sections are common
    total_elements = np.prod(sequences.shape)
    n_missing = int(total_elements * missing_rate)

    # Create missing patterns
    missing_indices = []

    # 70% random individual missing values
    n_random = int(n_missing * 0.7)
    random_indices = np.random.choice(total_elements, size=n_random, replace=False)
    missing_indices.extend(random_indices)

    # 30% missing chunks (more realistic for well logs)
    n_chunks = int(n_missing * 0.3)
    chunk_size = 3  # Average chunk size

    for _ in range(n_chunks // chunk_size):
        # Random sequence and position
        seq_idx = np.random.randint(0, n_sequences)
        start_pos = np.random.randint(0, max(1, seq_len - chunk_size))
        feature_idx = np.random.randint(0, n_features)

        # Add chunk indices
        for pos in range(start_pos, min(start_pos + chunk_size, seq_len)):
            flat_idx = seq_idx * (seq_len * n_features) + pos * n_features + feature_idx
            if flat_idx < total_elements:
                missing_indices.append(flat_idx)

    # Remove duplicates and limit to desired count
    missing_indices = list(set(missing_indices))[:n_missing]

    # Apply missing values
    flat_view = sequences_with_missing.flatten()
    flat_view[missing_indices] = np.nan
    sequences_with_missing = flat_view.reshape(sequences.shape)

    actual_missing_rate = len(missing_indices) / total_elements
    print(f"Introduced {actual_missing_rate:.1%} missing values ({len(missing_indices)} elements)")
    print(f"Missing pattern: {len(random_indices)} random + {len(missing_indices) - len(random_indices)} chunked")

    return sequences_with_missing
