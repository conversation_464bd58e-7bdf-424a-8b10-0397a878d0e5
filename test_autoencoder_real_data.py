#!/usr/bin/env python3
"""
Quick test of autoencoder with real data to verify NaN fixes
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
from sklearn.preprocessing import StandardScaler

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.simple_autoencoder import SimpleAutoencoder
from data_handler import create_sequences

def test_autoencoder_with_real_data():
    """Test autoencoder with a subset of real data"""
    print("=" * 60)
    print(" AUTOENCODER REAL DATA TEST")
    print("=" * 60)
    
    # Create synthetic data that mimics real well log data
    try:
        print("Creating synthetic well log data for testing...")
        
        # Create some synthetic data that mimics the real data structure
        np.random.seed(42)
        n_samples = 1000
        
        # Create synthetic well log data
        data = {
            'WELL': ['TEST_WELL'] * n_samples,
            'GR': np.random.normal(70, 20, n_samples),
            'NPHI': np.random.normal(0.15, 0.05, n_samples),
            'RHOB': np.random.normal(2.3, 0.1, n_samples),
            'RT': np.random.lognormal(1, 1, n_samples),
            'P-WAVE': np.random.normal(3000, 500, n_samples)
        }
        
        # Introduce realistic missing patterns
        for col in ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']:
            # Random missing values (20%)
            missing_indices = np.random.choice(n_samples, int(0.2 * n_samples), replace=False)
            for idx in missing_indices:
                data[col][idx] = np.nan
        
        df = pd.DataFrame(data)
        print(f"Created synthetic data: {df.shape}")
        print(f"Missing data percentages:")
        for col in ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']:
            missing_pct = df[col].isna().mean() * 100
            print(f"  {col}: {missing_pct:.1f}%")
        
        # Normalize data
        feature_cols = ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']
        scalers = {}
        
        for col in feature_cols:
            scaler = StandardScaler()
            valid_data = df[col].dropna().values.reshape(-1, 1)
            scaler.fit(valid_data)
            df[col] = scaler.transform(df[col].values.reshape(-1, 1)).flatten()
            scalers[col] = scaler
        
        print("Data normalized successfully")
        
        # Create sequences
        sequences = create_sequences(df, 'WELL', feature_cols, sequence_len=64)
        print(f"Created sequences shape: {sequences.shape}")
        
        # Introduce missing values in sequences
        missing_rate = 0.25
        n_missing = int(missing_rate * sequences.size)
        missing_indices = np.random.choice(sequences.size, n_missing, replace=False)
        sequences_with_missing = sequences.copy()
        sequences_with_missing.flat[missing_indices] = np.nan
        
        print(f"Introduced {missing_rate:.1%} missing values")
        
        # Convert to tensors
        train_data = torch.from_numpy(sequences_with_missing.astype(np.float32))
        truth_data = torch.from_numpy(sequences.astype(np.float32))
        
        print(f"Training data shape: {train_data.shape}")
        print(f"Truth data shape: {truth_data.shape}")
        
        # Test autoencoder
        print("\nTesting autoencoder...")
        autoencoder = SimpleAutoencoder(
            n_features=len(feature_cols),
            sequence_len=64,
            encoding_dim=32,
            epochs=5,  # Quick test
            batch_size=32,
            learning_rate=0.01
        )
        
        # Train
        autoencoder.fit(train_data, truth_data)
        
        # Test prediction
        print("\nTesting prediction...")
        predictions = autoencoder.predict(train_data)
        print(f"Predictions shape: {predictions.shape}")
        
        # Check for NaN values in predictions
        nan_count = torch.isnan(predictions).sum().item()
        if nan_count > 0:
            print(f"❌ Found {nan_count} NaN values in predictions")
            return False
        else:
            print("✅ No NaN values in predictions")
        
        # Calculate some basic metrics
        mse = torch.mean((predictions - truth_data) ** 2).item()
        print(f"MSE: {mse:.6f}")
        
        print("\n" + "=" * 60)
        print(" AUTOENCODER REAL DATA TEST PASSED! ✅")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_autoencoder_with_real_data()
    sys.exit(0 if success else 1)
