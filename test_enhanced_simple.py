#!/usr/bin/env python3
"""
Simple test for enhanced preprocessing without visualization.
"""

import numpy as np
import pandas as pd
from enhanced_preprocessing import enhanced_preprocessing_pipeline

def create_test_data():
    """Create simple test data."""
    np.random.seed(42)
    
    # Create 2 wells with 200 samples each
    data = []
    for well_id in ['WELL_A', 'WELL_B']:
        n_samples = 200
        
        # Create realistic well log data
        gr = 50 + 30 * np.random.normal(0, 1, n_samples)
        nphi = 0.2 + 0.1 * np.random.normal(0, 1, n_samples)
        rhob = 2.3 + 0.2 * np.random.normal(0, 1, n_samples)
        rt = np.exp(1 + 0.5 * np.random.normal(0, 1, n_samples))
        
        # Add some missing sections
        missing_start = np.random.randint(50, 100)
        missing_end = missing_start + 20
        gr[missing_start:missing_end] = np.nan
        nphi[missing_start:missing_end] = np.nan
        rhob[missing_start:missing_end] = np.nan
        rt[missing_start:missing_end] = np.nan
        
        well_df = pd.DataFrame({
            'WELL': well_id,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'RT': rt
        })
        data.append(well_df)
    
    return pd.concat(data, ignore_index=True)

def test_enhanced_preprocessing():
    """Test enhanced preprocessing pipeline."""
    print("Testing Enhanced Preprocessing Pipeline")
    print("=" * 50)
    
    # Create test data
    df = create_test_data()
    print(f"Test data: {len(df)} samples, {df['WELL'].nunique()} wells")
    
    # Test enhanced preprocessing
    try:
        sequences, sequences_missing, scalers, report = enhanced_preprocessing_pipeline(
            df=df,
            feature_cols=['GR', 'NPHI', 'RHOB'],
            target_col='RT',
            sequence_len=32,
            sequence_stride=16,
            missing_rate=0.15
        )
        
        print("\n✅ SUCCESS!")
        print(f"Clean sequences: {sequences.shape}")
        print(f"Missing sequences: {sequences_missing.shape}")
        print(f"Scalers: {list(scalers.keys())}")
        
        # Validate results
        assert sequences.shape[0] > 0, "No sequences created"
        assert sequences.shape[1] == 32, "Wrong sequence length"
        assert sequences.shape[2] == 4, "Wrong number of features"
        assert sequences_missing.shape == sequences.shape, "Shape mismatch"
        
        # Check missing rate
        original_nan_rate = np.isnan(sequences).mean()
        missing_nan_rate = np.isnan(sequences_missing).mean()
        
        print(f"Original NaN rate: {original_nan_rate:.1%}")
        print(f"Missing NaN rate: {missing_nan_rate:.1%}")
        
        assert missing_nan_rate > original_nan_rate, "Missing values not properly introduced"
        
        print("\n🎉 All validation checks passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_preprocessing()
    if success:
        print("\n✅ Enhanced preprocessing is ready for use!")
    else:
        print("\n❌ Enhanced preprocessing needs fixes.")
